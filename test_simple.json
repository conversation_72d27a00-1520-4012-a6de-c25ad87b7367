{"tasks": [{"id": "bacea4d4-a9e1-444d-9a41-ac965a13f1f3", "name": "测试任务1", "dependencies": [], "status": "completed", "process_id": 548513, "created_at": "2025-08-31T23:43:24.632976", "started_at": "2025-08-31T23:43:24.636157", "completed_at": "2025-08-31T23:43:24.637358", "error_message": null, "metadata": {"test": "data"}}, {"id": "7f64bd29-c085-4123-a353-58837c286150", "name": "测试任务2", "dependencies": ["bacea4d4-a9e1-444d-9a41-ac965a13f1f3"], "status": "waiting", "process_id": 548513, "created_at": "2025-08-31T23:43:24.634619", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}]}