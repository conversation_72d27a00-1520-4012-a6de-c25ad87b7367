import os
import sys
import json
import click
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加父目录到sys.path，以便导入task_scheduler包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from task_scheduler.models import Task, TaskStatus
from task_scheduler.storage import TaskStorage
from task_scheduler.utils import format_time_delta, build_dependency_graph, get_task_execution_order


@click.group()
def cli():
    """任务调度监控工具"""
    pass


@cli.command()
@click.option('--file', '-f', required=True, help='任务状态文件路径')
def list(file):
    """列出所有任务"""
    storage = TaskStorage(file)
    tasks = storage.get_all_tasks()
    
    if not tasks:
        click.echo("没有找到任务")
        return
    
    # 按状态分组
    waiting_tasks = [task for task in tasks if task.status == TaskStatus.WAITING]
    ready_tasks = [task for task in tasks if task.status == TaskStatus.READY]
    running_tasks = [task for task in tasks if task.status == TaskStatus.RUNNING]
    completed_tasks = [task for task in tasks if task.status == TaskStatus.COMPLETED]
    failed_tasks = [task for task in tasks if task.status == TaskStatus.FAILED]
    
    # 打印任务统计信息
    click.echo(f"总任务数: {len(tasks)}")
    click.echo(f"等待中: {len(waiting_tasks)}")
    click.echo(f"就绪: {len(ready_tasks)}")
    click.echo(f"执行中: {len(running_tasks)}")
    click.echo(f"已完成: {len(completed_tasks)}")
    click.echo(f"失败: {len(failed_tasks)}")
    click.echo("")
    
    # 打印任务详情
    click.echo("任务详情:")
    click.echo("-" * 80)
    click.echo(f"{'ID':<36} {'名称':<20} {'状态':<10} {'依赖数':<8} {'进程ID':<10} {'创建时间':<20}")
    click.echo("-" * 80)
    
    for task in tasks:
        status_color = {
            TaskStatus.WAITING: 'yellow',
            TaskStatus.READY: 'blue',
            TaskStatus.RUNNING: 'cyan',
            TaskStatus.COMPLETED: 'green',
            TaskStatus.FAILED: 'red'
        }.get(task.status, 'white')
        
        click.echo(
            f"{task.id:<36} {task.name:<20} "
            f"{click.style(task.status.value, fg=status_color):<10} "
            f"{len(task.dependencies):<8} {task.process_id:<10} "
            f"{task.created_at.strftime('%Y-%m-%d %H:%M:%S')}")


@cli.command()
@click.option('--file', '-f', required=True, help='任务状态文件路径')
@click.argument('task_id')
def show(file, task_id):
    """显示特定任务的详细信息"""
    storage = TaskStorage(file)
    task = storage.get_task(task_id)
    
    if task is None:
        click.echo(f"未找到任务: {task_id}")
        return
    
    # 获取依赖任务
    all_tasks = storage.get_all_tasks()
    task_map = {t.id: t for t in all_tasks}
    
    # 获取依赖该任务的任务
    dep_graph = build_dependency_graph(all_tasks)
    dependent_tasks = dep_graph.get(task.id, set())
    
    # 打印任务详情
    click.echo(f"任务ID: {task.id}")
    click.echo(f"名称: {task.name}")
    click.echo(f"状态: {click.style(task.status.value, fg=get_status_color(task.status))}")
    click.echo(f"进程ID: {task.process_id}")
    click.echo(f"创建时间: {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if task.started_at:
        click.echo(f"开始时间: {task.started_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if task.completed_at:
        click.echo(f"完成时间: {task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if task.started_at:
            duration = format_time_delta(task.started_at, task.completed_at)
            click.echo(f"执行时长: {duration}")
    
    if task.error_message:
        click.echo(f"错误信息: {task.error_message}")
    
    # 打印依赖任务
    click.echo("\n依赖任务:")
    if not task.dependencies:
        click.echo("  无")
    else:
        for dep_id in task.dependencies:
            dep_task = task_map.get(dep_id)
            if dep_task:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  {dep_id} - {dep_task.name} ({status_str})")
            else:
                click.echo(f"  {dep_id} - 未找到")
    
    # 打印依赖该任务的任务
    click.echo("\n被依赖:")
    if not dependent_tasks:
        click.echo("  无")
    else:
        for dep_id in dependent_tasks:
            dep_task = task_map.get(dep_id)
            if dep_task:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  {dep_id} - {dep_task.name} ({status_str})")
            else:
                click.echo(f"  {dep_id} - 未找到")
    
    # 打印元数据
    if task.metadata:
        click.echo("\n元数据:")
        for key, value in task.metadata.items():
            click.echo(f"  {key}: {value}")


@cli.command()
@click.option('--file', '-f', required=True, help='任务状态文件路径')
def graph(file):
    """显示任务依赖关系图"""
    storage = TaskStorage(file)
    tasks = storage.get_all_tasks()
    
    if not tasks:
        click.echo("没有找到任务")
        return
    
    # 获取任务执行顺序
    task_order = get_task_execution_order(tasks)
    task_map = {task.id: task for task in tasks}
    
    # 构建依赖图
    dep_graph = {}
    for task in tasks:
        deps = []
        for dep_id in task.dependencies:
            if dep_id in task_map:
                deps.append(dep_id)
        dep_graph[task.id] = deps
    
    # 打印依赖图
    click.echo("任务依赖关系图:")
    click.echo("")
    
    for task_id in task_order:
        task = task_map.get(task_id)
        if not task:
            continue
        
        # 打印当前任务
        status_str = click.style(task.status.value, fg=get_status_color(task.status))
        click.echo(f"{task.id} - {task.name} ({status_str})")
        
        # 打印依赖关系
        for dep_id in dep_graph.get(task.id, []):
            dep_task = task_map.get(dep_id)
            if dep_task:
                click.echo(f"  ↳ 依赖 {dep_id} - {dep_task.name}")
        
        click.echo("")


@cli.command()
@click.option('--file', '-f', required=True, help='任务状态文件路径')
def watch(file):
    """实时监控任务状态变化"""
    import time
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    
    class TaskFileHandler(FileSystemEventHandler):
        def on_modified(self, event):
            if event.src_path == os.path.abspath(file):
                os.system('cls' if os.name == 'nt' else 'clear')
                click.echo(f"任务状态更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                click.echo("")
                
                # 调用list命令显示任务列表
                storage = TaskStorage(file)
                tasks = storage.get_all_tasks()
                
                if not tasks:
                    click.echo("没有找到任务")
                    return
                
                # 按状态分组
                waiting_tasks = [task for task in tasks if task.status == TaskStatus.WAITING]
                ready_tasks = [task for task in tasks if task.status == TaskStatus.READY]
                running_tasks = [task for task in tasks if task.status == TaskStatus.RUNNING]
                completed_tasks = [task for task in tasks if task.status == TaskStatus.COMPLETED]
                failed_tasks = [task for task in tasks if task.status == TaskStatus.FAILED]
                
                # 打印任务统计信息
                click.echo(f"总任务数: {len(tasks)}")
                click.echo(f"等待中: {len(waiting_tasks)}")
                click.echo(f"就绪: {len(ready_tasks)}")
                click.echo(f"执行中: {len(running_tasks)}")
                click.echo(f"已完成: {len(completed_tasks)}")
                click.echo(f"失败: {len(failed_tasks)}")
                click.echo("")
                
                # 打印任务详情
                click.echo("任务详情:")
                click.echo("-" * 80)
                click.echo(f"{'ID':<36} {'名称':<20} {'状态':<10} {'依赖数':<8} {'进程ID':<10} {'创建时间':<20}")
                click.echo("-" * 80)
                
                for task in tasks:
                    status_color = get_status_color(task.status)
                    
                    click.echo(
                        f"{task.id:<36} {task.name:<20} "
                        f"{click.style(task.status.value, fg=status_color):<10} "
                        f"{len(task.dependencies):<8} {task.process_id:<10} "
                        f"{task.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    click.echo(f"开始监控任务状态文件: {file}")
    click.echo("按 Ctrl+C 退出")
    click.echo("")
    
    # 确保文件存在
    if not os.path.exists(file):
        click.echo(f"文件不存在: {file}")
        return
    
    # 显示初始状态
    os.system('cls' if os.name == 'nt' else 'clear')
    click.echo(f"任务状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    click.echo("")
    ctx = click.Context(list)
    ctx.invoke(list, file=file)
    
    # 设置文件监控
    event_handler = TaskFileHandler()
    observer = Observer()
    observer.schedule(event_handler, path=os.path.dirname(os.path.abspath(file)), recursive=False)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    
    observer.join()


def get_status_color(status):
    """获取任务状态对应的颜色"""
    return {
        TaskStatus.WAITING: 'yellow',
        TaskStatus.READY: 'blue',
        TaskStatus.RUNNING: 'cyan',
        TaskStatus.COMPLETED: 'green',
        TaskStatus.FAILED: 'red'
    }.get(status, 'white')


def main():
    """命令行入口"""
    cli()


if __name__ == '__main__':
    main()