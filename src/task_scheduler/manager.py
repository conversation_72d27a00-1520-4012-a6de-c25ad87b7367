import os
import time
from datetime import datetime
from typing import List, Optional, Dict, Any

from .models import Task, TaskStatus
from .storage import TaskStorage


class TaskManager:
    """任务管理器，提供任务注册、状态更新、依赖检查等功能"""

    def __init__(self, storage_path: str):
        """初始化任务管理器

        Args:
            storage_path: 任务状态文件路径
        """
        self.storage = TaskStorage(storage_path)
        self.storage_path = storage_path
        # 设置日志文件路径（与storage_path在同一目录）
        storage_dir = os.path.dirname(os.path.abspath(storage_path))
        log_filename = os.path.splitext(os.path.basename(storage_path))[0] + ".log"
        self.log_file = os.path.join(storage_dir, log_filename)

    def _log(self, level: str, message: str):
        """记录日志到控制台和文件

        Args:
            level: 日志级别 (INFO, WARNING, ERROR)
            message: 日志消息
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]
        log_entry = f"[{timestamp}] [TaskManager] {level}: {message}"

        # 打印到控制台
        print(log_entry)

        # 写入到日志文件
        try:
            # 确保日志目录存在
            os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"[{timestamp}] [TaskManager] ERROR: 写入日志文件失败: {e}")

    def _log_info(self, message: str):
        """记录INFO级别日志"""
        self._log("INFO", message)

    def _log_warning(self, message: str):
        """记录WARNING级别日志"""
        self._log("WARNING", message)

    def _log_error(self, message: str):
        """记录ERROR级别日志"""
        self._log("ERROR", message)

    def register_task(self, name: str, dependencies: Optional[List[str]] = None) -> str:
        """注册新任务

        Args:
            name: 任务名称
            dependencies: 前置任务ID列表（必须是已存在的任务ID）

        Returns:
            task_id: 任务ID

        Raises:
            ValueError: 当依赖任务ID不存在时
        """
        self._log_info(f"开始注册任务: {name}")

        # 处理依赖列表
        dep_list = [] if dependencies is None else dependencies

        # 验证所有依赖任务ID是否存在
        if dep_list:
            self._log_info(f"验证依赖任务: {dep_list}")
            existing_tasks = {task.id: task for task in self.storage.get_all_tasks()}

            for dep_id in dep_list:
                if dep_id not in existing_tasks:
                    error_msg = f"依赖任务ID不存在: {dep_id}"
                    self._log_error(error_msg)
                    raise ValueError(error_msg)

            # 打印依赖任务的当前状态
            self._log_info("依赖任务状态:")
            for dep_id in dep_list:
                dep_task = existing_tasks[dep_id]
                self._log_info(f"  - {dep_task.name} ({dep_id}): {dep_task.status.value}")

        # 创建任务对象
        task = Task(name=name, dependencies=dep_list)

        # 保存任务
        success = self.storage.save_task(task)
        if not success:
            error_msg = f"任务注册失败: {name} (可能已存在相同ID的任务)"
            self._log_error(error_msg)
            raise RuntimeError(error_msg)

        self._log_info(f"任务注册成功: {name} (ID: {task.id})")

        # 检查依赖是否已满足
        completed_task_ids = self.storage.get_completed_task_ids()
        if task.is_ready(completed_task_ids):
            task.status = TaskStatus.READY
            self.storage.update_task(task)
            self._log_info(f"任务状态更新为就绪: {name}")
        else:
            self._log_info(f"任务等待依赖完成: {name}")

        return task.id

    def wait_for_dependencies(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """等待前置任务完成

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒），None表示无限等待

        Returns:
            bool: 是否所有依赖都已完成
        """
        task = self.storage.get_task(task_id)
        if task is None:
            self._log_error(f"等待依赖失败: 任务不存在 (ID: {task_id})")
            return False

        self._log_info(f"开始等待依赖: {task.name} (ID: {task_id})")

        # 如果任务已经准备好或正在运行，则不需要等待
        if task.status in [TaskStatus.READY, TaskStatus.RUNNING]:
            self._log_info(f"任务已就绪，无需等待: {task.name}")
            return True

        # 如果任务已完成或失败，则返回相应状态
        if task.status == TaskStatus.COMPLETED:
            self._log_info(f"任务已完成，无需等待: {task.name}")
            return True
        if task.status == TaskStatus.FAILED:
            self._log_warning(f"任务已失败，停止等待: {task.name}")
            return False

        # 等待依赖任务完成
        start_time = time.time()
        check_interval = 0.5  # 检查间隔（秒）
        logged_waiting = False

        while True:
            # 检查是否超时
            if timeout is not None and time.time() - start_time > timeout:
                self._log_warning(f"等待依赖超时: {task.name} (超时时间: {timeout}s)")
                return False

            # 获取已完成任务ID列表
            completed_task_ids = self.storage.get_completed_task_ids()

            # 检查任务是否准备好执行
            task = self.storage.get_task(task_id)  # 重新获取任务，以获取最新状态
            if task is None:
                self._log_error(f"等待过程中任务消失: {task_id}")
                return False

            if task.is_ready(completed_task_ids):
                # 更新任务状态为就绪
                task.status = TaskStatus.READY
                self.storage.update_task(task)
                self._log_info(f"依赖已满足，任务就绪: {task.name}")
                return True

            # 只在第一次打印等待信息，避免日志过多
            if not logged_waiting:
                pending_deps = [dep_id for dep_id in task.dependencies if dep_id not in completed_task_ids]
                self._log_info(f"等待依赖任务完成: {task.name}, 待完成依赖: {pending_deps}")
                logged_waiting = True

            # 等待一段时间后再次检查
            time.sleep(check_interval)

    def start_task(self, task_id: str) -> bool:
        """标记任务为执行中状态

        Args:
            task_id: 任务ID

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            self._log_error(f"启动任务失败: 任务不存在 (ID: {task_id})")
            return False

        self._log_info(f"尝试启动任务: {task.name} (ID: {task_id}), 当前状态: {task.status.value}")

        # 只有就绪状态的任务才能开始执行
        if task.status != TaskStatus.READY and task.status != TaskStatus.WAITING:
            self._log_warning(f"启动任务失败: 任务状态不允许启动 ({task.status.value}): {task.name}")
            return False

        # 更新任务状态
        task.start()
        success = self.storage.update_task(task)

        if success:
            self._log_info(f"任务启动成功: {task.name} (ID: {task_id})")
        else:
            self._log_error(f"任务启动失败: 状态更新失败: {task.name}")

        return success

    def complete_task(self, task_id: str, metadata: Optional[dict] = None) -> bool:
        """标记任务为已完成状态

        Args:
            task_id: 任务ID
            metadata: 任务元数据（可选）

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            self._log_error(f"完成任务失败: 任务不存在 (ID: {task_id})")
            return False

        self._log_info(f"尝试完成任务: {task.name} (ID: {task_id}), 当前状态: {task.status.value}")

        # 只有执行中状态的任务才能标记为已完成
        if task.status != TaskStatus.RUNNING:
            self._log_warning(f"完成任务失败: 任务状态不允许完成 ({task.status.value}): {task.name}")
            return False

        # 更新任务状态和元数据
        task.complete()
        if metadata:
            task.metadata.update(metadata)
            self._log_info(f"任务元数据已更新: {task.name}, 元数据: {metadata}")

        success = self.storage.update_task(task)

        if success:
            self._log_info(f"任务完成成功: {task.name} (ID: {task_id})")
        else:
            self._log_error(f"任务完成失败: 状态更新失败: {task.name}")

        return success

    def fail_task(self, task_id: str, error_message: Optional[str] = None) -> bool:
        """标记任务为失败状态

        Args:
            task_id: 任务ID
            error_message: 错误信息

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            self._log_error(f"标记任务失败失败: 任务不存在 (ID: {task_id})")
            return False

        self._log_info(f"尝试标记任务失败: {task.name} (ID: {task_id}), 当前状态: {task.status.value}")

        # 只有执行中或就绪状态的任务才能标记为失败
        if task.status != TaskStatus.RUNNING and task.status != TaskStatus.READY:
            self._log_warning(f"标记任务失败失败: 任务状态不允许失败 ({task.status.value}): {task.name}")
            return False

        # 更新任务状态
        task.fail(error_message)
        success = self.storage.update_task(task)

        if success:
            self._log_warning(f"任务已标记为失败: {task.name} (ID: {task_id}), 错误信息: {error_message or '无'}")
        else:
            self._log_error(f"标记任务失败失败: 状态更新失败: {task.name}")

        return success

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务

        Args:
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务对象，不存在则返回None
        """
        return self.storage.get_task(task_id)

    def get_all_tasks(self) -> List[Task]:
        """获取所有任务

        Returns:
            List[Task]: 任务列表
        """
        return self.storage.get_all_tasks()

    def get_tasks_by_name(self, name: str) -> List[Task]:
        """根据名称获取任务

        Args:
            name: 任务名称

        Returns:
            List[Task]: 匹配的任务列表
        """
        return self.storage.get_tasks_by_name(name)

    def get_pending_tasks(self) -> List[Task]:
        """获取所有等待中的任务

        Returns:
            List[Task]: 等待中的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.WAITING]

    def get_ready_tasks(self) -> List[Task]:
        """获取所有就绪的任务

        Returns:
            List[Task]: 就绪的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.READY]

    def get_running_tasks(self) -> List[Task]:
        """获取所有执行中的任务

        Returns:
            List[Task]: 执行中的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.RUNNING]

    def get_completed_tasks(self) -> List[Task]:
        """获取所有已完成的任务

        Returns:
            List[Task]: 已完成的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.COMPLETED]

    def get_failed_tasks(self) -> List[Task]:
        """获取所有失败的任务

        Returns:
            List[Task]: 失败的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.FAILED]

    def update_task_metadata(self, task_id: str, metadata: Dict[str, Any]) -> bool:
        """更新任务元数据

        Args:
            task_id: 任务ID
            metadata: 元数据字典

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            self._log_error(f"更新元数据失败: 任务不存在 (ID: {task_id})")
            return False

        self._log_info(f"更新任务元数据: {task.name} (ID: {task_id}), 元数据: {metadata}")

        # 更新元数据
        task.metadata.update(metadata)
        success = self.storage.update_task(task)

        if success:
            self._log_info(f"元数据更新成功: {task.name}")
        else:
            self._log_error(f"元数据更新失败: {task.name}")

        return success

    def clear_all_tasks(self) -> None:
        """清空所有任务"""
        task_count = len(self.storage.get_all_tasks())
        self._log_info(f"清空所有任务: 共 {task_count} 个任务")
        self.storage.clear_all_tasks()
        self._log_info("所有任务已清空")
