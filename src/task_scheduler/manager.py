import os
import time
from typing import List, Optional, Dict, Any

from .models import Task, TaskStatus
from .storage import TaskStorage


class TaskManager:
    """任务管理器，提供任务注册、状态更新、依赖检查等功能"""

    def __init__(self, storage_path: str):
        """初始化任务管理器

        Args:
            storage_path: 任务状态文件路径
        """
        self.storage = TaskStorage(storage_path)

    def register_task(self, name: str, dependencies: List[str] = None) -> str:
        """注册新任务

        Args:
            name: 任务名称
            dependencies: 前置任务名称列表

        Returns:
            task_id: 任务ID
        """
        # 创建任务对象
        task = Task(name=name, dependencies=[] if dependencies is None else dependencies)

        # 保存任务
        self.storage.save_task(task)

        # 检查依赖是否已满足
        completed_task_ids = self.storage.get_completed_task_ids()
        completed_task_names = self._get_completed_task_names()
        if task.is_ready(completed_task_ids + completed_task_names):
            task.status = TaskStatus.READY
            self.storage.update_task(task)

        return task.id

    def _get_completed_task_names(self) -> List[str]:
        """获取所有已完成任务的名称

        Returns:
            List[str]: 已完成任务名称列表
        """
        tasks = self.storage.get_all_tasks()
        return [task.name for task in tasks if task.status == TaskStatus.COMPLETED]

    def wait_for_dependencies(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """等待前置任务完成

        Args:
            task_id: 任务ID
            timeout: 超时时间（秒），None表示无限等待

        Returns:
            bool: 是否所有依赖都已完成
        """
        task = self.storage.get_task(task_id)
        if task is None:
            return False

        # 如果任务已经准备好或正在运行，则不需要等待
        if task.status in [TaskStatus.READY, TaskStatus.RUNNING]:
            return True

        # 如果任务已完成或失败，则返回相应状态
        if task.status == TaskStatus.COMPLETED:
            return True
        if task.status == TaskStatus.FAILED:
            return False

        # 等待依赖任务完成
        start_time = time.time()
        check_interval = 0.5  # 检查间隔（秒）

        while True:
            # 检查是否超时
            if timeout is not None and time.time() - start_time > timeout:
                return False

            # 获取已完成任务ID列表和名称列表
            completed_task_ids = self.storage.get_completed_task_ids()
            completed_task_names = self._get_completed_task_names()

            # 检查任务是否准备好执行
            task = self.storage.get_task(task_id)  # 重新获取任务，以获取最新状态
            if task is None:
                return False

            if task.is_ready(completed_task_ids + completed_task_names):
                # 更新任务状态为就绪
                task.status = TaskStatus.READY
                self.storage.update_task(task)
                return True

            # 等待一段时间后再次检查
            time.sleep(check_interval)

    def start_task(self, task_id: str) -> bool:
        """标记任务为执行中状态

        Args:
            task_id: 任务ID

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            return False

        # 只有就绪状态的任务才能开始执行
        if task.status != TaskStatus.READY and task.status != TaskStatus.WAITING:
            return False

        # 更新任务状态
        task.start()
        return self.storage.update_task(task)

    def complete_task(self, task_id: str, metadata: Optional[dict] = None) -> bool:
        """标记任务为已完成状态

        Args:
            task_id: 任务ID
            metadata: 任务元数据（可选）

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            return False

        # 只有执行中状态的任务才能标记为已完成
        if task.status != TaskStatus.RUNNING:
            return False

        # 更新任务状态和元数据
        task.complete()
        if metadata:
            task.metadata.update(metadata)
        return self.storage.update_task(task)

    def fail_task(self, task_id: str, error_message: Optional[str] = None) -> bool:
        """标记任务为失败状态

        Args:
            task_id: 任务ID
            error_message: 错误信息

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            return False

        # 只有执行中或就绪状态的任务才能标记为失败
        if task.status != TaskStatus.RUNNING and task.status != TaskStatus.READY:
            return False

        # 更新任务状态
        task.fail(error_message)
        return self.storage.update_task(task)

    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务

        Args:
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务对象，不存在则返回None
        """
        return self.storage.get_task(task_id)

    def get_all_tasks(self) -> List[Task]:
        """获取所有任务

        Returns:
            List[Task]: 任务列表
        """
        return self.storage.get_all_tasks()

    def get_tasks_by_name(self, name: str) -> List[Task]:
        """根据名称获取任务

        Args:
            name: 任务名称

        Returns:
            List[Task]: 匹配的任务列表
        """
        return self.storage.get_tasks_by_name(name)

    def get_pending_tasks(self) -> List[Task]:
        """获取所有等待中的任务

        Returns:
            List[Task]: 等待中的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.WAITING]

    def get_ready_tasks(self) -> List[Task]:
        """获取所有就绪的任务

        Returns:
            List[Task]: 就绪的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.READY]

    def get_running_tasks(self) -> List[Task]:
        """获取所有执行中的任务

        Returns:
            List[Task]: 执行中的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.RUNNING]

    def get_completed_tasks(self) -> List[Task]:
        """获取所有已完成的任务

        Returns:
            List[Task]: 已完成的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.COMPLETED]

    def get_failed_tasks(self) -> List[Task]:
        """获取所有失败的任务

        Returns:
            List[Task]: 失败的任务列表
        """
        tasks = self.storage.get_all_tasks()
        return [task for task in tasks if task.status == TaskStatus.FAILED]

    def update_task_metadata(self, task_id: str, metadata: Dict[str, Any]) -> bool:
        """更新任务元数据

        Args:
            task_id: 任务ID
            metadata: 元数据字典

        Returns:
            bool: 操作是否成功
        """
        task = self.storage.get_task(task_id)
        if task is None:
            return False

        # 更新元数据
        task.metadata.update(metadata)
        return self.storage.update_task(task)

    def clear_all_tasks(self) -> None:
        """清空所有任务"""
        self.storage.clear_all_tasks()
