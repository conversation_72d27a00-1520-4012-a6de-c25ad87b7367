{"tasks": [{"id": "e5f2315b-d84a-4f6b-b472-ae8c40f7b3b8", "name": "数据收集", "dependencies": [], "status": "completed", "process_id": 537669, "created_at": "2025-08-31T23:33:38.519054", "started_at": "2025-08-31T23:33:38.524429", "completed_at": "2025-08-31T23:33:39.525955", "error_message": null, "metadata": {"records": 10000, "source": "database"}}, {"id": "d43f81a8-6f1e-4194-b80d-4ce9c5e6e5b9", "name": "数据清洗", "dependencies": ["e5f2315b-d84a-4f6b-b472-ae8c40f7b3b8"], "status": "completed", "process_id": 537669, "created_at": "2025-08-31T23:33:38.521173", "started_at": "2025-08-31T23:33:39.527773", "completed_at": "2025-08-31T23:33:40.528771", "error_message": null, "metadata": {"cleaned_records": 9500, "removed": 500}}, {"id": "24cd2f99-3d20-4641-bde0-5c5db0bc7f30", "name": "数据验证", "dependencies": ["e5f2315b-d84a-4f6b-b472-ae8c40f7b3b8", "d43f81a8-6f1e-4194-b80d-4ce9c5e6e5b9"], "status": "completed", "process_id": 537669, "created_at": "2025-08-31T23:33:38.522404", "started_at": "2025-08-31T23:33:40.530904", "completed_at": "2025-08-31T23:33:41.532106", "error_message": null, "metadata": {"validation_passed": true, "errors": 0}}]}