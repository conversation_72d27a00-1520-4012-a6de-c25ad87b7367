{"tasks": [{"id": "9354bc2a-b125-4237-a95e-bcf8f3dda1ea", "name": "数据收集", "dependencies": [], "status": "completed", "process_id": 547084, "created_at": "2025-08-31T23:42:13.899892", "started_at": "2025-08-31T23:42:13.905831", "completed_at": "2025-08-31T23:42:14.907861", "error_message": null, "metadata": {"records": 10000, "source": "database"}}, {"id": "d2f44ba3-a121-4cb4-8a99-cf6640a8dcc7", "name": "数据清洗", "dependencies": ["9354bc2a-b125-4237-a95e-bcf8f3dda1ea"], "status": "completed", "process_id": 547084, "created_at": "2025-08-31T23:42:13.901985", "started_at": "2025-08-31T23:42:14.910007", "completed_at": "2025-08-31T23:42:15.911188", "error_message": null, "metadata": {"cleaned_records": 9500, "removed": 500}}, {"id": "0765ee55-700c-4b06-b2ec-b40063161654", "name": "数据验证", "dependencies": ["9354bc2a-b125-4237-a95e-bcf8f3dda1ea", "d2f44ba3-a121-4cb4-8a99-cf6640a8dcc7"], "status": "completed", "process_id": 547084, "created_at": "2025-08-31T23:42:13.903429", "started_at": "2025-08-31T23:42:15.914390", "completed_at": "2025-08-31T23:42:16.915752", "error_message": null, "metadata": {"validation_passed": true, "errors": 0}}]}