{"tasks": [{"id": "e5da89f9-d151-496f-8683-115921294839", "name": "数据收集", "dependencies": [], "status": "completed", "process_id": 556596, "created_at": "2025-08-31T23:50:49.380462", "started_at": "2025-08-31T23:50:49.387811", "completed_at": "2025-08-31T23:50:50.389574", "error_message": null, "metadata": {"records": 10000, "source": "database"}}, {"id": "8526461e-e01a-4a00-81e6-75348379b7ea", "name": "数据清洗", "dependencies": ["e5da89f9-d151-496f-8683-115921294839"], "status": "completed", "process_id": 556596, "created_at": "2025-08-31T23:50:49.383023", "started_at": "2025-08-31T23:50:50.392047", "completed_at": "2025-08-31T23:50:52.208765", "error_message": null, "metadata": {"cleaned_records": 9500, "removed": 500}}, {"id": "77f95aeb-52a2-4d15-a2e4-66095a636da7", "name": "数据验证", "dependencies": ["e5da89f9-d151-496f-8683-115921294839", "8526461e-e01a-4a00-81e6-75348379b7ea"], "status": "completed", "process_id": 556596, "created_at": "2025-08-31T23:50:49.385020", "started_at": "2025-08-31T23:50:52.211208", "completed_at": "2025-08-31T23:50:53.212352", "error_message": null, "metadata": {"validation_passed": true, "errors": 0}}]}