{"tasks": [{"id": "8a18bcff-9947-4c80-b928-9d9e534adcf2", "name": "数据准备", "dependencies": [], "status": "completed", "process_id": 328369, "created_at": "2025-08-31T20:14:42.870404", "started_at": "2025-08-31T20:14:42.872796", "completed_at": "2025-08-31T20:14:44.873939", "error_message": null, "metadata": {"execution_time": 2, "completed_at": "2025-08-31T20:14:44.873494", "hostname": "yang"}}, {"id": "24ecc5e5-aa7c-4389-bad6-9107425ad2e0", "name": "数据验证", "dependencies": ["数据准备"], "status": "completed", "process_id": 328379, "created_at": "2025-08-31T20:14:43.370870", "started_at": "2025-08-31T20:14:44.926605", "completed_at": "2025-08-31T20:14:45.927609", "error_message": null, "metadata": {"execution_time": 1, "completed_at": "2025-08-31T20:14:45.927198", "hostname": "yang"}}, {"id": "850ff218-48ff-4cc1-a274-2cc50e5739d0", "name": "特征工程", "dependencies": ["数据验证"], "status": "completed", "process_id": 328399, "created_at": "2025-08-31T20:14:43.872077", "started_at": "2025-08-31T20:14:46.029143", "completed_at": "2025-08-31T20:14:49.030126", "error_message": null, "metadata": {"execution_time": 3, "completed_at": "2025-08-31T20:14:49.029726", "hostname": "yang"}}, {"id": "4ce8ebc0-e9e1-4f8d-a208-147073bd6be4", "name": "模型训练A", "dependencies": ["特征工程"], "status": "completed", "process_id": 328404, "created_at": "2025-08-31T20:14:44.373123", "started_at": "2025-08-31T20:14:49.485006", "completed_at": "2025-08-31T20:14:54.327838", "error_message": null, "metadata": {"execution_time": 4, "completed_at": "2025-08-31T20:14:54.327484", "hostname": "yang"}}, {"id": "72f107dc-0a50-4781-9188-2805b3156e28", "name": "模型训练B", "dependencies": ["特征工程"], "status": "completed", "process_id": 328423, "created_at": "2025-08-31T20:14:44.874279", "started_at": "2025-08-31T20:14:49.085355", "completed_at": "2025-08-31T20:14:53.928176", "error_message": null, "metadata": {"execution_time": 4, "completed_at": "2025-08-31T20:14:53.927774", "hostname": "yang"}}, {"id": "0e32df08-d597-4054-8b71-c3afc9e28030", "name": "模型评估", "dependencies": ["模型训练A", "模型训练B"], "status": "completed", "process_id": 328427, "created_at": "2025-08-31T20:14:45.375560", "started_at": "2025-08-31T20:14:54.734961", "completed_at": "2025-08-31T20:14:56.736579", "error_message": null, "metadata": {"execution_time": 2, "completed_at": "2025-08-31T20:14:56.736184", "hostname": "yang"}}, {"id": "a66455c7-80eb-4b3e-a3e4-0e3eab7d02a6", "name": "模型部署", "dependencies": ["模型评估"], "status": "completed", "process_id": 328439, "created_at": "2025-08-31T20:14:45.876493", "started_at": "2025-08-31T20:14:56.788795", "completed_at": "2025-08-31T20:14:59.789871", "error_message": null, "metadata": {"execution_time": 3, "completed_at": "2025-08-31T20:14:59.789462", "hostname": "yang"}}]}