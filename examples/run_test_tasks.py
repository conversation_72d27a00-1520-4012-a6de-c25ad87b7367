#!/usr/bin/env python3
"""
运行测试任务的脚本
同时启动任务A和任务B，演示任务依赖关系
"""

import os
import sys
import time
import subprocess
from multiprocessing import Process

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from task_scheduler import TaskManager


def run_task_script(script_name):
    """运行任务脚本"""
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    subprocess.run([sys.executable, script_path])


def main():
    print("=== 任务调度器测试 ===")
    print("任务A: 数据下载（无依赖）")
    print("任务B: 数据处理（依赖任务A）")
    print()
    
    # 清理之前的任务状态文件
    task_file = "/tmp/test_tasks.json"
    if os.path.exists(task_file):
        os.remove(task_file)
        print("已清理之前的任务状态文件")
    
    print("启动任务...")
    print()
    
    # 创建进程来运行任务
    # 注意：任务B会等待任务A完成，所以可以同时启动
    process_a = Process(target=run_task_script, args=("test_task_a.py",))
    process_b = Process(target=run_task_script, args=("test_task_b.py",))
    
    # 启动任务B（会等待任务A）
    process_b.start()
    time.sleep(0.5)  # 稍微延迟，确保任务B先注册
    
    # 启动任务A
    process_a.start()
    
    # 等待所有进程完成
    process_a.join()
    process_b.join()
    
    print()
    print("=== 所有任务完成 ===")
    
    # 显示最终任务状态
    print("\n最终任务状态:")
    manager = TaskManager(task_file)
    tasks = manager.get_all_tasks()
    for task in tasks:
        print(f"- {task.name}: {task.status.value}")
        if task.metadata:
            print(f"  元数据: {task.metadata}")


if __name__ == "__main__":
    main()
