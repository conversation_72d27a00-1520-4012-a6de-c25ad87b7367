#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务调度工具使用示例

这个示例展示了如何使用任务调度工具来协调多个独立进程之间的任务执行顺序。
示例中创建了一个简单的工作流，包含多个相互依赖的任务。
"""

import os
import sys
import time
import random
import argparse
from datetime import datetime
from multiprocessing import Process

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../")))

from src.task_scheduler.models import Task, TaskStatus
from src.task_scheduler.manager import TaskManager


def run_task(task_name, task_file, dependencies=None, sleep_time=None, fail_probability=0):
    """
    运行一个任务，包括注册、等待依赖、执行和完成

    Args:
        task_name: 任务名称
        task_file: 任务状态文件路径
        dependencies: 依赖任务ID列表
        sleep_time: 模拟任务执行时间（秒）
        fail_probability: 任务失败概率（0-100）
    """
    # 创建任务管理器
    manager = TaskManager(task_file)

    # 注册任务
    task_id = manager.register_task(task_name, dependencies or [])
    print(f"[{task_name}] 已注册任务，ID: {task_id}")

    # 等待依赖任务完成
    manager.wait_for_dependencies(task_id)

    # 开始执行任务
    manager.start_task(task_id)
    print(f"[{task_name}] 开始执行任务")

    # 模拟任务执行
    execution_time = sleep_time if sleep_time is not None else random.randint(1, 5)
    print(f"[{task_name}] 执行中... (预计 {execution_time} 秒)")
    time.sleep(execution_time)

    # 随机失败
    if random.randint(1, 100) <= fail_probability:
        error_msg = f"任务 {task_name} 执行失败"
        print(f"[{task_name}] {error_msg}")
        manager.fail_task(task_id, error_msg)
        return

    # 完成任务
    metadata = {
        "execution_time": execution_time,
        "completed_at": datetime.now().isoformat(),
        "hostname": os.uname().nodename,
    }
    manager.complete_task(task_id, metadata)
    print(f"[{task_name}] 任务已完成")


def create_workflow(task_file):
    """
    创建一个示例工作流，包含多个相互依赖的任务

    Args:
        task_file: 任务状态文件路径
    """
    # 清空任务文件
    if os.path.exists(task_file):
        os.remove(task_file)

    # 定义工作流任务
    workflow = [
        # 任务名称, 依赖任务, 执行时间, 失败概率
        ("数据准备", [], 2, 0),
        ("数据验证", ["数据准备"], 1, 10),
        ("特征工程", ["数据验证"], 3, 5),
        ("模型训练A", ["特征工程"], 4, 15),
        ("模型训练B", ["特征工程"], 4, 15),
        ("模型评估", ["模型训练A", "模型训练B"], 2, 5),
        ("模型部署", ["模型评估"], 3, 10),
    ]

    # 启动进程
    processes = []
    for task_name, dependencies, sleep_time, fail_prob in workflow:
        # 将任务名称转换为任务ID（在实际场景中，应该使用TaskManager注册后返回的ID）
        dep_ids = dependencies

        # 创建并启动进程
        p = Process(target=run_task, args=(task_name, task_file, dep_ids, sleep_time, fail_prob))
        processes.append(p)
        p.start()
        # 稍微延迟，确保任务注册顺序
        time.sleep(0.5)

    # 等待所有进程完成
    for p in processes:
        p.join()


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="任务调度工具示例")
    parser.add_argument("--task-file", default="./task_scheduler_example.json", help="任务状态文件路径")
    args = parser.parse_args()

    print(f"启动任务调度示例工作流，任务状态文件: {args.task_file}")
    print("")

    # 创建并执行工作流
    create_workflow(args.task_file)

    print("")
    print(f"工作流执行完成，可以使用以下命令查看任务状态:")
    print(f"python -m src.task_scheduler.cli.monitor list --file {args.task_file}")
    print(f"python -m src.task_scheduler.cli.monitor graph --file {args.task_file}")


if __name__ == "__main__":
    main()
