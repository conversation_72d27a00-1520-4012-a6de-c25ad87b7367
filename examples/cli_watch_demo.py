#!/usr/bin/env python3
"""
演示task_scheduler CLI的实时监控功能
这个脚本会创建任务并慢速执行，方便观察watch命令的效果
"""

import os
import sys
import time
import threading

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def slow_task_execution(task_file):
    """慢速执行任务，方便观察监控效果"""
    manager = TaskManager(task_file)
    
    # 创建任务链
    print("创建任务链...")
    task1_id = manager.register_task("初始化环境")
    task2_id = manager.register_task("下载数据", dependencies=["初始化环境"])
    task3_id = manager.register_task("数据预处理", dependencies=["下载数据"])
    task4_id = manager.register_task("模型训练", dependencies=["数据预处理"])
    task5_id = manager.register_task("结果验证", dependencies=["模型训练"])
    
    tasks = [
        (task1_id, "初始化环境", 3),
        (task2_id, "下载数据", 5),
        (task3_id, "数据预处理", 4),
        (task4_id, "模型训练", 6),
        (task5_id, "结果验证", 2)
    ]
    
    print("开始执行任务...")
    print("提示: 在另一个终端运行以下命令来实时监控:")
    print(f"python src/task_scheduler/cli/monitor.py watch --file {task_file}")
    print()
    
    for task_id, task_name, duration in tasks:
        # 等待依赖任务完成
        print(f"[{task_name}] 等待依赖任务完成...")
        manager.wait_for_dependencies(task_id)
        
        # 开始执行任务
        print(f"[{task_name}] 开始执行...")
        manager.start_task(task_id)
        
        # 模拟长时间执行
        for i in range(duration):
            time.sleep(1)
            progress = int((i + 1) / duration * 100)
            print(f"[{task_name}] 进度: {progress}%")
        
        # 完成任务
        metadata = {
            "duration": f"{duration}s",
            "status": "success",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        manager.complete_task(task_id, metadata)
        print(f"[{task_name}] 完成!")
        print()


def print_instructions():
    """打印使用说明"""
    print("=== Task Scheduler CLI 实时监控演示 ===")
    print()
    print("这个演示会创建5个相互依赖的任务，并慢速执行它们。")
    print("您可以在执行过程中使用CLI工具来监控任务状态。")
    print()
    print("建议的使用步骤:")
    print("1. 运行此脚本: python examples/cli_watch_demo.py")
    print("2. 在另一个终端窗口运行监控命令")
    print("3. 观察任务状态的实时变化")
    print()
    print("可用的CLI命令:")
    print("- 实时监控: python src/task_scheduler/cli/monitor.py watch --file /tmp/watch_demo_tasks.json")
    print("- 列出任务: python src/task_scheduler/cli/monitor.py list --file /tmp/watch_demo_tasks.json")
    print("- 依赖关系图: python src/task_scheduler/cli/monitor.py graph --file /tmp/watch_demo_tasks.json")
    print("- 任务详情: python src/task_scheduler/cli/monitor.py show --file /tmp/watch_demo_tasks.json <task_id>")
    print()
    print("=" * 60)
    print()


def main():
    task_file = "/tmp/watch_demo_tasks.json"
    
    # 清理之前的任务状态文件
    if os.path.exists(task_file):
        os.remove(task_file)
    
    print_instructions()
    
    # 询问用户是否准备好
    input("按 Enter 键开始演示...")
    print()
    
    # 执行任务
    slow_task_execution(task_file)
    
    print("=" * 60)
    print("演示完成!")
    print(f"任务状态文件保存在: {task_file}")
    print()
    print("您现在可以使用CLI命令查看最终结果:")
    print(f"python src/task_scheduler/cli/monitor.py list --file {task_file}")
    print("=" * 60)


if __name__ == "__main__":
    main()
