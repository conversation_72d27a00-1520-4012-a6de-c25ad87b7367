#!/usr/bin/env python3
"""
创建一些未完成的任务用于测试clear命令的错误处理
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def main():
    task_file = "/tmp/test_incomplete_tasks.json"
    
    # 清理之前的任务状态文件
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    # 创建一些任务，有些完成，有些未完成
    task1_id = manager.register_task("已完成任务")
    task2_id = manager.register_task("运行中任务", dependencies=["已完成任务"])
    task3_id = manager.register_task("等待中任务", dependencies=["运行中任务"])
    
    print(f"创建了3个任务，保存在: {task_file}")
    
    # 完成第一个任务
    manager.wait_for_dependencies(task1_id)
    manager.start_task(task1_id)
    manager.complete_task(task1_id, {"status": "success"})
    print("任务1已完成")
    
    # 开始第二个任务但不完成
    manager.wait_for_dependencies(task2_id)
    manager.start_task(task2_id)
    print("任务2已开始但未完成")
    
    # 第三个任务保持等待状态
    print("任务3保持等待状态")
    
    print(f"\n现在可以测试clear命令:")
    print(f"python src/task_scheduler/cli.py clear --file {task_file}")
    print(f"python src/task_scheduler/cli.py clear --file {task_file} --force")


if __name__ == "__main__":
    main()
