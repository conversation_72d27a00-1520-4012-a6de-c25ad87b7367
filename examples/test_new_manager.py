#!/usr/bin/env python3
"""
测试新的TaskManager功能：
1. dependencies必须是存在的task_id
2. 注册成功后打印依赖任务状态
3. 所有接口调用都有日志打印
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def test_valid_dependencies():
    """测试有效的依赖关系"""
    print("=== 测试有效的依赖关系 ===")
    
    task_file = "./test_new_manager.json"
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    # 创建第一个任务（无依赖）
    task1_id = manager.register_task("数据收集")
    print(f"任务1 ID: {task1_id}")
    
    # 创建第二个任务（依赖任务1）
    task2_id = manager.register_task("数据处理", dependencies=[task1_id])
    print(f"任务2 ID: {task2_id}")
    
    # 创建第三个任务（依赖任务1和任务2）
    task3_id = manager.register_task("数据分析", dependencies=[task1_id, task2_id])
    print(f"任务3 ID: {task3_id}")
    
    return manager, task1_id, task2_id, task3_id


def test_invalid_dependencies():
    """测试无效的依赖关系"""
    print("\n=== 测试无效的依赖关系 ===")
    
    task_file = "./test_invalid_deps.json"
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    try:
        # 尝试创建依赖不存在任务ID的任务
        manager.register_task("无效任务", dependencies=["non-existent-id"])
        print("错误：应该抛出异常但没有抛出")
    except ValueError as e:
        print(f"正确捕获异常: {e}")
    except Exception as e:
        print(f"意外异常: {e}")


def test_task_lifecycle():
    """测试任务生命周期和日志"""
    print("\n=== 测试任务生命周期和日志 ===")
    
    manager, task1_id, task2_id, task3_id = test_valid_dependencies()
    
    # 测试任务执行流程
    print("\n--- 执行任务1 ---")
    manager.wait_for_dependencies(task1_id)
    manager.start_task(task1_id)
    time.sleep(1)  # 模拟任务执行
    manager.complete_task(task1_id, {"records": 1000, "size": "10MB"})
    
    print("\n--- 执行任务2 ---")
    manager.wait_for_dependencies(task2_id)
    manager.start_task(task2_id)
    time.sleep(1)  # 模拟任务执行
    manager.complete_task(task2_id, {"processed": 950, "errors": 50})
    
    print("\n--- 尝试执行任务3 ---")
    manager.wait_for_dependencies(task3_id, timeout=1)  # 短超时测试
    manager.start_task(task3_id)
    
    print("\n--- 测试失败任务 ---")
    manager.fail_task(task3_id, "模拟任务失败")
    
    print("\n--- 测试更新元数据 ---")
    manager.update_task_metadata(task1_id, {"updated_at": "2023-12-01"})


def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")
    
    task_file = "./test_errors.json"
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    # 测试不存在的任务ID
    print("\n--- 测试不存在的任务ID ---")
    manager.start_task("non-existent-id")
    manager.complete_task("non-existent-id")
    manager.fail_task("non-existent-id")
    manager.update_task_metadata("non-existent-id", {"test": "data"})
    
    # 测试状态转换错误
    print("\n--- 测试状态转换错误 ---")
    task_id = manager.register_task("测试任务")
    
    # 尝试完成未启动的任务
    manager.complete_task(task_id)
    
    # 启动任务
    manager.start_task(task_id)
    
    # 尝试重复启动
    manager.start_task(task_id)


def main():
    print("测试新的TaskManager功能")
    print("=" * 50)
    
    # 测试有效依赖
    test_valid_dependencies()
    
    # 测试无效依赖
    test_invalid_dependencies()
    
    # 测试任务生命周期
    test_task_lifecycle()
    
    # 测试错误情况
    test_error_cases()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意观察日志输出，验证以下功能：")
    print("1. 注册任务时验证依赖ID是否存在")
    print("2. 注册成功后打印依赖任务状态")
    print("3. 所有接口调用都有相应的日志")
    print("4. 错误情况有适当的错误日志")


if __name__ == "__main__":
    main()
