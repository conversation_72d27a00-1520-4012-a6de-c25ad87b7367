#!/usr/bin/env python3
"""
测试任务B - 依赖任务A完成后才能执行
这个任务会等待任务A（数据下载）完成后再开始执行
"""

import time
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, "../src")

from task_scheduler import TaskManager


def main():
    # 使用相同的任务文件
    task_file = "./test_tasks.json"

    # 创建任务管理器
    manager = TaskManager(task_file)

    # 注册任务B，依赖于任务A（数据下载）
    task_id = manager.register_task("数据处理", dependencies=["数据下载"])
    print(f"[任务B] 已注册任务 '数据处理'，ID: {task_id}")
    print("[任务B] 依赖任务: 数据下载")

    # 等待依赖任务完成
    print("[任务B] 等待依赖任务 '数据下载' 完成...")
    manager.wait_for_dependencies(task_id)
    print("[任务B] 依赖任务已完成，开始执行")

    # 开始执行任务
    success = manager.start_task(task_id)
    if not success:
        print("[任务B] 启动任务失败")
        return

    print("[任务B] 正在处理数据...")

    # 模拟任务执行（数据处理需要2秒）
    for i in range(2):
        time.sleep(1)
        print(f"[任务B] 处理进度: {(i + 1) * 50}%")

    # 标记任务完成
    success = manager.complete_task(task_id, metadata={"processed_records": 1000, "processing_time": "2s"})
    if success:
        print("[任务B] 数据处理完成！")
    else:
        print("[任务B] 标记任务完成失败")


if __name__ == "__main__":
    main()
