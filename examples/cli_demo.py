#!/usr/bin/env python3
"""
使用task_scheduler CLI工具的演示脚本
展示如何使用命令行工具监控和管理任务
"""

import os
import sys
import time
import subprocess
from multiprocessing import Process

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def create_demo_tasks():
    """创建演示任务"""
    task_file = "/tmp/cli_demo_tasks.json"
    
    # 清理之前的任务状态文件
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    print("创建演示任务...")
    
    # 创建一个复杂的任务依赖链
    task1_id = manager.register_task("数据收集")
    task2_id = manager.register_task("数据清洗", dependencies=["数据收集"])
    task3_id = manager.register_task("特征工程", dependencies=["数据清洗"])
    task4_id = manager.register_task("模型训练A", dependencies=["特征工程"])
    task5_id = manager.register_task("模型训练B", dependencies=["特征工程"])
    task6_id = manager.register_task("模型评估", dependencies=["模型训练A", "模型训练B"])
    task7_id = manager.register_task("模型部署", dependencies=["模型评估"])
    
    print(f"已创建 7 个任务，任务状态文件: {task_file}")
    return task_file


def run_task_simulation(task_file):
    """运行任务模拟"""
    manager = TaskManager(task_file)
    
    # 获取所有任务
    tasks = manager.get_all_tasks()
    task_map = {task.name: task for task in tasks}
    
    # 按顺序执行任务
    execution_order = [
        "数据收集", "数据清洗", "特征工程", 
        "模型训练A", "模型训练B", "模型评估", "模型部署"
    ]
    
    for task_name in execution_order:
        if task_name not in task_map:
            continue
            
        task = task_map[task_name]
        
        # 等待依赖任务完成
        manager.wait_for_dependencies(task.id)
        
        # 开始执行任务
        manager.start_task(task.id)
        print(f"[模拟] 开始执行任务: {task_name}")
        
        # 模拟任务执行时间
        execution_time = 2 if "训练" in task_name else 1
        time.sleep(execution_time)
        
        # 完成任务
        metadata = {
            "execution_time": f"{execution_time}s",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        manager.complete_task(task.id, metadata)
        print(f"[模拟] 完成任务: {task_name}")


def demo_cli_commands(task_file):
    """演示CLI命令的使用"""
    cli_script = os.path.join(os.path.dirname(__file__), "..", "src", "task_scheduler", "cli", "monitor.py")
    
    print("\n" + "="*60)
    print("CLI工具演示")
    print("="*60)
    
    # 1. 列出所有任务
    print("\n1. 列出所有任务:")
    print(f"命令: python {cli_script} list --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "list", "--file", task_file])
    
    # 2. 显示任务依赖关系图
    print("\n2. 显示任务依赖关系图:")
    print(f"命令: python {cli_script} graph --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "graph", "--file", task_file])
    
    # 3. 显示特定任务详情
    manager = TaskManager(task_file)
    tasks = manager.get_all_tasks()
    if tasks:
        sample_task = tasks[0]
        print(f"\n3. 显示任务详情 (示例任务: {sample_task.name}):")
        print(f"命令: python {cli_script} show --file {task_file} {sample_task.id}")
        print("-" * 40)
        subprocess.run([sys.executable, cli_script, "show", "--file", task_file, sample_task.id])


def main():
    print("=== Task Scheduler CLI 演示 ===")
    print()
    
    # 创建演示任务
    task_file = create_demo_tasks()
    
    print("\n启动任务模拟进程...")
    # 在后台启动任务模拟
    simulation_process = Process(target=run_task_simulation, args=(task_file,))
    simulation_process.start()
    
    # 等待一下让任务开始执行
    time.sleep(0.5)
    
    # 演示CLI命令
    demo_cli_commands(task_file)
    
    # 等待模拟进程完成
    simulation_process.join()
    
    print("\n" + "="*60)
    print("演示完成！")
    print("="*60)
    
    print(f"\n任务状态文件保存在: {task_file}")
    print("\n您可以手动运行以下命令来探索更多功能:")
    print(f"  python src/task_scheduler/cli/monitor.py list --file {task_file}")
    print(f"  python src/task_scheduler/cli/monitor.py graph --file {task_file}")
    print(f"  python src/task_scheduler/cli/monitor.py watch --file {task_file}")
    print("\n注意: watch 命令会实时监控任务状态变化，按 Ctrl+C 退出")


if __name__ == "__main__":
    main()
