#!/usr/bin/env python3
"""
演示新的统一CLI工具 (cli.py) 的所有功能
包括新增的clear命令
"""

import os
import sys
import time
import subprocess
from multiprocessing import Process

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def create_demo_tasks():
    """创建演示任务"""
    task_file = "./test_tasks.json"

    # 清理之前的任务状态文件
    if os.path.exists(task_file):
        os.remove(task_file)

    manager = TaskManager(task_file)

    print("创建演示任务...")

    # 创建任务链
    task1_id = manager.register_task("数据收集")
    task2_id = manager.register_task("数据清洗", dependencies=["数据收集"])
    task3_id = manager.register_task("特征工程", dependencies=["数据清洗"])
    task4_id = manager.register_task("模型训练", dependencies=["特征工程"])
    task5_id = manager.register_task("模型评估", dependencies=["模型训练"])

    print(f"已创建 5 个任务，任务状态文件: {task_file}")
    return task_file


def run_partial_tasks(task_file):
    """运行部分任务，留一些未完成"""
    manager = TaskManager(task_file)

    # 获取所有任务
    tasks = manager.get_all_tasks()
    task_map = {task.name: task for task in tasks}

    # 完成前3个任务
    for task_name in ["数据收集", "数据清洗", "特征工程"]:
        if task_name not in task_map:
            continue

        task = task_map[task_name]

        # 等待依赖任务完成
        manager.wait_for_dependencies(task.id)

        # 开始执行任务
        manager.start_task(task.id)
        print(f"[模拟] 开始执行任务: {task_name}")

        # 模拟任务执行时间
        time.sleep(1)

        # 完成任务
        metadata = {"execution_time": "1s", "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")}
        manager.complete_task(task.id, metadata)
        print(f"[模拟] 完成任务: {task_name}")

    # 开始第4个任务但不完成
    task = task_map["模型训练"]
    manager.wait_for_dependencies(task.id)
    manager.start_task(task.id)
    print(f"[模拟] 开始执行任务: 模型训练 (但不完成)")


def demo_all_commands(task_file):
    """演示所有CLI命令"""
    cli_script = os.path.join(os.path.dirname(__file__), "..", "src", "task_scheduler", "cli.py")

    print("\n" + "=" * 60)
    print("新版CLI工具演示")
    print("=" * 60)

    # 1. 列出所有任务
    print("\n1. 列出所有任务:")
    print(f"命令: python {cli_script} list --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "list", "--file", task_file])

    # 2. 显示任务依赖关系图
    print("\n2. 显示任务依赖关系图:")
    print(f"命令: python {cli_script} graph --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "graph", "--file", task_file])

    # 3. 显示特定任务详情
    manager = TaskManager(task_file)
    tasks = manager.get_all_tasks()
    if tasks:
        sample_task = tasks[0]
        print(f"\n3. 显示任务详情 (示例任务: {sample_task.name}):")
        print(f"命令: python {cli_script} show --file {task_file} {sample_task.id}")
        print("-" * 40)
        subprocess.run([sys.executable, cli_script, "show", "--file", task_file, sample_task.id])

    # 4. 尝试清空任务（应该失败，因为有未完成的任务）
    print(f"\n4. 尝试清空任务 (应该失败):")
    print(f"命令: python {cli_script} clear --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "clear", "--file", task_file])

    # 5. 强制清空所有任务
    print(f"\n5. 强制清空所有任务:")
    print(f"命令: echo 'y' | python {cli_script} clear --file {task_file} --force")
    print("-" * 40)
    subprocess.run(f"echo 'y' | python {cli_script} clear --file {task_file} --force", shell=True)

    # 6. 验证任务已清空
    print(f"\n6. 验证任务已清空:")
    print(f"命令: python {cli_script} list --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "list", "--file", task_file])


def main():
    print("=== 新版Task Scheduler CLI 演示 ===")
    print()
    print("新功能:")
    print("- 统一的CLI工具 (cli.py)")
    print("- 新增 clear 命令用于删除任务")
    print("- 智能检查：只有所有任务完成才能清空")
    print("- --force 参数可强制删除所有任务")
    print()

    # 创建演示任务
    task_file = create_demo_tasks()

    print("\n启动任务模拟进程...")
    # 在后台启动任务模拟
    simulation_process = Process(target=run_partial_tasks, args=(task_file,))
    simulation_process.start()

    # 等待一下让任务开始执行
    time.sleep(2)

    # 演示CLI命令
    demo_all_commands(task_file)

    # 等待模拟进程完成
    simulation_process.join()

    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)

    print(f"\n统一CLI工具位置: src/task_scheduler/cli.py")
    print("\n可用命令:")
    print("  list   - 列出所有任务")
    print("  show   - 显示特定任务详情")
    print("  graph  - 显示任务依赖关系图")
    print("  watch  - 实时监控任务状态变化")
    print("  clear  - 清空已完成的任务")
    print("  clear --force - 强制清空所有任务")
    print()
    print("使用示例:")
    print("  python src/task_scheduler/cli.py --help")
    print("  python src/task_scheduler/cli.py list --file /path/to/tasks.json")
    print("  python src/task_scheduler/cli.py clear --file /path/to/tasks.json")


if __name__ == "__main__":
    main()
