# TaskScheduler 示例代码

本目录包含了TaskScheduler的示例代码和测试脚本。

## 文件说明

### 核心功能演示

#### `demo_task_id_dependencies.py`
- **功能**: 完整演示TaskManager的新功能
- **特点**: 
  - 使用task_id作为依赖（新API）
  - 展示依赖验证和状态显示
  - 包含错误处理演示
  - 完整的任务生命周期
- **运行**: `python examples/demo_task_id_dependencies.py`

### 基础测试脚本

#### `test_task_a.py`
- **功能**: 独立任务测试（无依赖）
- **特点**: 模拟数据下载任务，执行3秒
- **运行**: `python examples/test_task_a.py`

#### `test_task_b.py`
- **功能**: 依赖任务测试（依赖任务A）
- **特点**: 模拟数据处理任务，等待任务A完成后执行
- **运行**: `python examples/test_task_b.py`
- **注意**: 需要先运行test_task_a.py创建依赖任务

### 系统测试

#### `test_logging.py`
- **功能**: 测试新的日志系统
- **特点**: 
  - 验证日志同时输出到控制台和文件
  - 测试不同目录的日志文件生成
  - 验证日志文件命名规则
- **运行**: `python examples/test_logging.py`

## 使用建议

### 快速开始
```bash
# 1. 演示完整功能
python examples/demo_task_id_dependencies.py

# 2. 测试日志系统
python examples/test_logging.py
```

### 依赖任务演示
```bash
# 1. 先运行独立任务
python examples/test_task_a.py

# 2. 再运行依赖任务
python examples/test_task_b.py
```

## CLI工具使用

所有示例都会生成任务状态文件，可以使用CLI工具查看：

```bash
# 列出任务
python src/task_scheduler/cli.py list --file <task_file>

# 显示依赖关系图
python src/task_scheduler/cli.py graph --file <task_file>

# 实时监控
python src/task_scheduler/cli.py watch --file <task_file>

# 清空已完成任务
python src/task_scheduler/cli.py clear --file <task_file>
```

## 注意事项

1. **依赖关系**: 新版本只支持使用task_id作为依赖，不再支持任务名称
2. **日志文件**: 日志文件会自动生成在与任务状态文件相同的目录中
3. **文件路径**: 所有示例使用相对路径，确保在项目根目录运行
