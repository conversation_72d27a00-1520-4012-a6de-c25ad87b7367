#!/usr/bin/env python3
"""
Task Scheduler CLI 使用指南
展示所有CLI命令的用法和示例
"""

import os
import sys
import subprocess

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from task_scheduler import TaskManager


def create_sample_tasks():
    """创建示例任务用于演示"""
    task_file = "/tmp/cli_guide_tasks.json"
    
    # 清理之前的任务状态文件
    if os.path.exists(task_file):
        os.remove(task_file)
    
    manager = TaskManager(task_file)
    
    # 创建一些示例任务
    task1_id = manager.register_task("数据获取")
    task2_id = manager.register_task("数据验证", dependencies=["数据获取"])
    task3_id = manager.register_task("数据转换", dependencies=["数据验证"])
    task4_id = manager.register_task("模型训练", dependencies=["数据转换"])
    task5_id = manager.register_task("模型评估", dependencies=["模型训练"])
    
    # 模拟一些任务已完成
    manager.wait_for_dependencies(task1_id)
    manager.start_task(task1_id)
    manager.complete_task(task1_id, {"records": 1000, "size": "50MB"})
    
    manager.wait_for_dependencies(task2_id)
    manager.start_task(task2_id)
    manager.complete_task(task2_id, {"valid_records": 950, "errors": 50})
    
    # 模拟一个正在运行的任务
    manager.wait_for_dependencies(task3_id)
    manager.start_task(task3_id)
    
    return task_file


def print_cli_guide():
    """打印CLI使用指南"""
    print("=" * 80)
    print("Task Scheduler CLI 使用指南")
    print("=" * 80)
    print()
    
    cli_script = "src/task_scheduler/cli/monitor.py"
    
    print("CLI工具位置:")
    print(f"  {cli_script}")
    print()
    
    print("基本语法:")
    print(f"  python {cli_script} <command> [options]")
    print()
    
    print("可用命令:")
    print()
    
    # 1. list 命令
    print("1. list - 列出所有任务")
    print("   用途: 查看所有任务的概览信息")
    print("   语法: python {cli_script} list --file <task_file>")
    print("   示例: python {cli_script} list --file /tmp/tasks.json")
    print("   输出: 任务统计信息和详细列表")
    print()
    
    # 2. show 命令
    print("2. show - 显示特定任务详情")
    print("   用途: 查看单个任务的详细信息")
    print("   语法: python {cli_script} show --file <task_file> <task_id>")
    print("   示例: python {cli_script} show --file /tmp/tasks.json abc123")
    print("   输出: 任务详情、依赖关系、元数据等")
    print()
    
    # 3. graph 命令
    print("3. graph - 显示任务依赖关系图")
    print("   用途: 可视化任务之间的依赖关系")
    print("   语法: python {cli_script} graph --file <task_file>")
    print("   示例: python {cli_script} graph --file /tmp/tasks.json")
    print("   输出: 任务依赖关系的图形化表示")
    print()
    
    # 4. watch 命令
    print("4. watch - 实时监控任务状态")
    print("   用途: 实时监控任务状态变化")
    print("   语法: python {cli_script} watch --file <task_file>")
    print("   示例: python {cli_script} watch --file /tmp/tasks.json")
    print("   输出: 实时更新的任务状态列表")
    print("   注意: 按 Ctrl+C 退出监控")
    print()
    
    print("任务状态说明:")
    print("  waiting   - 等待中 (黄色)")
    print("  ready     - 就绪   (蓝色)")
    print("  running   - 执行中 (青色)")
    print("  completed - 已完成 (绿色)")
    print("  failed    - 失败   (红色)")
    print()


def demonstrate_commands(task_file):
    """演示各个CLI命令"""
    cli_script = os.path.join(os.path.dirname(__file__), "..", "src", "task_scheduler", "cli", "monitor.py")
    
    print("=" * 80)
    print("CLI命令演示")
    print("=" * 80)
    
    # 演示 list 命令
    print("\n>>> 演示 list 命令:")
    print(f"命令: python {cli_script} list --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "list", "--file", task_file])
    
    # 演示 graph 命令
    print(f"\n>>> 演示 graph 命令:")
    print(f"命令: python {cli_script} graph --file {task_file}")
    print("-" * 40)
    subprocess.run([sys.executable, cli_script, "graph", "--file", task_file])
    
    # 演示 show 命令
    manager = TaskManager(task_file)
    tasks = manager.get_all_tasks()
    if tasks:
        sample_task = tasks[0]
        print(f"\n>>> 演示 show 命令 (任务: {sample_task.name}):")
        print(f"命令: python {cli_script} show --file {task_file} {sample_task.id}")
        print("-" * 40)
        subprocess.run([sys.executable, cli_script, "show", "--file", task_file, sample_task.id])


def main():
    print_cli_guide()
    
    # 创建示例任务
    print("创建示例任务用于演示...")
    task_file = create_sample_tasks()
    print(f"示例任务已创建，保存在: {task_file}")
    
    # 演示命令
    demonstrate_commands(task_file)
    
    print("\n" + "=" * 80)
    print("使用提示:")
    print("=" * 80)
    print(f"1. 任务状态文件: {task_file}")
    print("2. 您可以手动运行上述任何命令来探索功能")
    print("3. 使用 watch 命令可以实时监控任务状态变化")
    print("4. 在实际使用中，将任务文件路径替换为您的实际文件路径")
    print()
    print("常用命令快速参考:")
    print(f"  python {os.path.join('src', 'task_scheduler', 'cli', 'monitor.py')} list --file {task_file}")
    print(f"  python {os.path.join('src', 'task_scheduler', 'cli', 'monitor.py')} graph --file {task_file}")
    print(f"  python {os.path.join('src', 'task_scheduler', 'cli', 'monitor.py')} watch --file {task_file}")
    print("=" * 80)


if __name__ == "__main__":
    main()
