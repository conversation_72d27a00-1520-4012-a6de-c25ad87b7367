{"tasks": [{"id": "bbba59aa-66a6-4ea2-ba4e-e7125ca379a9", "name": "数据准备", "dependencies": [], "status": "completed", "process_id": 330390, "created_at": "2025-08-31T20:17:22.936936", "started_at": "2025-08-31T20:17:22.939392", "completed_at": "2025-08-31T20:17:24.940228", "error_message": null, "metadata": {"execution_time": 2, "completed_at": "2025-08-31T20:17:24.939845", "hostname": "yang"}}, {"id": "52592291-777b-41df-8b66-69d978acf499", "name": "数据验证", "dependencies": ["数据准备"], "status": "completed", "process_id": 330407, "created_at": "2025-08-31T20:17:23.437784", "started_at": "2025-08-31T20:17:25.043850", "completed_at": "2025-08-31T20:17:26.094970", "error_message": null, "metadata": {"execution_time": 1, "completed_at": "2025-08-31T20:17:26.044361", "hostname": "yang"}}, {"id": "f5ac068f-0a99-4b6c-a617-8aca0522af85", "name": "特征工程", "dependencies": ["数据验证"], "status": "completed", "process_id": 330424, "created_at": "2025-08-31T20:17:23.938892", "started_at": "2025-08-31T20:17:26.546623", "completed_at": "2025-08-31T20:17:29.547649", "error_message": null, "metadata": {"execution_time": 3, "completed_at": "2025-08-31T20:17:29.547265", "hostname": "yang"}}, {"id": "a25bce6a-11d0-436c-9e6d-ac729bc49223", "name": "模型训练A", "dependencies": ["特征工程"], "status": "completed", "process_id": 330428, "created_at": "2025-08-31T20:17:24.440154", "started_at": "2025-08-31T20:17:29.652443", "completed_at": "2025-08-31T20:17:33.653391", "error_message": null, "metadata": {"execution_time": 4, "completed_at": "2025-08-31T20:17:33.653046", "hostname": "yang"}}, {"id": "fadd2da8-66a8-4e4c-aebc-d593ac5bb027", "name": "模型训练B", "dependencies": ["特征工程"], "status": "failed", "process_id": 330442, "created_at": "2025-08-31T20:17:24.941206", "started_at": "2025-08-31T20:17:29.951419", "completed_at": "2025-08-31T20:17:33.953061", "error_message": "任务 模型训练B 执行失败", "metadata": {}}, {"id": "ffe308af-140d-4c0f-932b-d43476e0f694", "name": "模型评估", "dependencies": ["模型训练A", "模型训练B"], "status": "waiting", "process_id": 330446, "created_at": "2025-08-31T20:17:25.442586", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "56687726-8508-4ef1-9e88-da566eecdc56", "name": "模型部署", "dependencies": ["模型评估"], "status": "waiting", "process_id": 330457, "created_at": "2025-08-31T20:17:25.943397", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}]}