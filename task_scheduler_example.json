{"tasks": [{"id": "e66c396a-7c05-4775-9c35-3b2f0f0961c7", "name": "数据准备", "dependencies": [], "status": "completed", "process_id": 313544, "created_at": "2025-08-31T19:50:46.244502", "started_at": "2025-08-31T19:50:46.246371", "completed_at": "2025-08-31T19:50:48.247390", "error_message": null, "metadata": {}}, {"id": "fa305855-666f-448e-80b0-38cc9a48d11f", "name": "数据验证", "dependencies": ["数据准备"], "status": "waiting", "process_id": 313561, "created_at": "2025-08-31T19:50:46.745082", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "343969e8-5a49-4cae-9d5e-1205039b4ebc", "name": "特征工程", "dependencies": ["数据验证"], "status": "waiting", "process_id": 313581, "created_at": "2025-08-31T19:50:47.246312", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "fea6d50e-eca8-4e49-92d0-f11134f79eb9", "name": "模型训练A", "dependencies": ["特征工程"], "status": "waiting", "process_id": 313592, "created_at": "2025-08-31T19:50:47.747149", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "b15a5189-02a3-4f1d-a179-6f8729a902f1", "name": "模型训练B", "dependencies": ["特征工程"], "status": "waiting", "process_id": 313607, "created_at": "2025-08-31T19:50:48.248259", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "1da79b83-acef-40f2-ac95-1c6d0704897b", "name": "模型评估", "dependencies": ["模型训练A", "模型训练B"], "status": "waiting", "process_id": 313615, "created_at": "2025-08-31T19:50:48.749289", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}, {"id": "b709c26b-1c7c-446b-89c1-ec552b75e918", "name": "模型部署", "dependencies": ["模型评估"], "status": "waiting", "process_id": 313623, "created_at": "2025-08-31T19:50:49.250195", "started_at": null, "completed_at": null, "error_message": null, "metadata": {}}]}