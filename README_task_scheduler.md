# 任务调度工具

一个用于协调不同独立进程间任务执行顺序的轻量级工具。

## 功能特点

- 允许独立进程注册任务，并指定任务名称和前置依赖任务
- 基于任务依赖关系自动调度任务执行
- 在任务完成后通知依赖于该任务的其他任务
- 将任务信息持久化到文件中，供所有进程读写
- 提供查看工具，用于监控任务执行状态和依赖关系

## 安装

```bash
pip install -e .
```

## 使用示例

### 任务注册和执行

```python
# 进程A
from task_scheduler import TaskManager

# 初始化任务管理器
task_manager = TaskManager("/path/to/tasks.json")

# 注册任务
task_id = task_manager.register_task("数据预处理")

# 标记任务开始执行
task_manager.start_task(task_id)

# 执行任务...

# 标记任务完成
task_manager.complete_task(task_id)
```

```python
# 进程B
from task_scheduler import TaskManager

# 初始化任务管理器
task_manager = TaskManager("/path/to/tasks.json")

# 注册依赖于数据预处理的任务
task_id = task_manager.register_task("模型训练", dependencies=["数据预处理"])

# 等待依赖任务完成
task_manager.wait_for_dependencies(task_id)

# 标记任务开始执行
task_manager.start_task(task_id)

# 执行任务...

# 标记任务完成
task_manager.complete_task(task_id)
```

### 任务监控

```bash
# 查看所有任务状态
$ task-monitor --file /path/to/tasks.json list

# 查看任务依赖关系图
$ task-monitor --file /path/to/tasks.json graph

# 查看特定任务详情
$ task-monitor --file /path/to/tasks.json show task-id
```

## 项目结构

```
task_scheduler/
├── __init__.py
├── manager.py     # 任务管理器实现
├── storage.py     # 任务存储实现
├── models.py      # 数据模型定义
├── utils.py       # 工具函数
└── cli/
    ├── __init__.py
    └── monitor.py  # 任务监控工具实现
```

## 依赖

- Python 3.8+
- filelock: 用于实现文件锁
- watchdog: 用于监控文件变化
- click: 用于构建命令行工具